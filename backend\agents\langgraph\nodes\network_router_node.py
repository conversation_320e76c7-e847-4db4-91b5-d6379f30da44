"""
Network Router Node for LangGraph Network Architecture.

This node enables any-to-any agent communication by routing messages
and coordinating workflows between agents in the network.
"""

import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import asyncio

from ..states.network_state import (
    NetworkDatageniusState, NetworkCommunicationType, NetworkMessagePriority,
    add_network_message, update_network_topology
)
from ..core.agent_registry import network_registry
from ..core.agent_discovery import discovery_service, DiscoveryRequest, DiscoveryStrategy
from ..communication.messaging_system import messaging_system, NetworkMessage

logger = logging.getLogger(__name__)


class NetworkRouterNode:
    """
    Network router node that enables any-to-any agent communication.
    
    This node:
    - Routes messages between agents
    - Coordinates multi-agent workflows
    - Manages network topology
    - Handles agent discovery and selection
    - Facilitates dynamic collaboration
    """

    def __init__(self):
        """Initialize the network router node."""
        self.name = "network_router"
        self.active_routes: Dict[str, Dict[str, Any]] = {}
        self.workflow_coordinators: Dict[str, str] = {}
        self.message_handlers = {
            NetworkCommunicationType.DIRECT_MESSAGE: self._handle_direct_message,
            NetworkCommunicationType.CONSULTATION_REQUEST: self._handle_consultation_request,
            NetworkCommunicationType.CONSULTATION_RESPONSE: self._handle_consultation_response,
            NetworkCommunicationType.DELEGATION: self._handle_delegation,
            NetworkCommunicationType.COLLABORATION_INVITE: self._handle_collaboration_invite,
            NetworkCommunicationType.HANDOFF: self._handle_handoff,
            NetworkCommunicationType.CONSENSUS_VOTE: self._handle_consensus_vote,
            NetworkCommunicationType.INSIGHT_SHARE: self._handle_insight_share,
            NetworkCommunicationType.STATUS_UPDATE: self._handle_status_update
        }
        
        logger.info("NetworkRouterNode initialized")

    async def __call__(self, state: NetworkDatageniusState) -> NetworkDatageniusState:
        """
        Process network routing and communication.
        
        Args:
            state: Current network state
            
        Returns:
            Updated state after processing network communications
        """
        try:
            logger.debug("Processing network routing")
            
            # Update network topology
            state = await self._update_network_topology(state)
            
            # Process pending network messages
            state = await self._process_network_messages(state)
            
            # Handle agent discovery requests
            state = await self._handle_discovery_requests(state)
            
            # Coordinate active workflows
            state = await self._coordinate_workflows(state)
            
            # Update network health status
            state = await self._update_network_health(state)
            
            # Determine next action
            next_action = await self._determine_next_action(state)
            if next_action:
                state["next_action"] = next_action
            
            state["last_router_update"] = datetime.now().isoformat()
            
            return state
            
        except Exception as e:
            logger.error(f"Error in network router: {e}")
            state["errors"] = state.get("errors", [])
            state["errors"].append({
                "node": "network_router",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            return state

    async def _update_network_topology(self, state: NetworkDatageniusState) -> NetworkDatageniusState:
        """Update the network topology with current agents."""
        try:
            # Get all registered agents
            agents = network_registry.get_all_agents()
            agent_data = []
            
            for agent in agents:
                agent_info = {
                    "agent_id": agent.agent_id,
                    "agent_type": agent.agent_type,
                    "name": agent.name,
                    "status": agent.status.value,
                    "capabilities": [cap.name for cap in agent.capabilities],
                    "current_load": agent.current_load,
                    "is_available": agent.is_available()
                }
                agent_data.append(agent_info)
            
            # Get network connections
            connections = network_registry.get_network_topology()
            
            # Update state
            state = update_network_topology(state, agent_data, connections)
            
            logger.debug(f"Updated network topology with {len(agent_data)} agents")
            
        except Exception as e:
            logger.error(f"Error updating network topology: {e}")
        
        return state

    async def _process_network_messages(self, state: NetworkDatageniusState) -> NetworkDatageniusState:
        """Process pending network messages."""
        try:
            current_agent = state.get("current_agent")
            if not current_agent:
                return state
            
            # Get pending messages for current agent
            messages = await messaging_system.get_messages_for_agent(
                agent_id=current_agent,
                limit=10,
                mark_as_read=True
            )
            
            # Process each message
            for message in messages:
                try:
                    handler = self.message_handlers.get(message.message_type)
                    if handler:
                        state = await handler(state, message)
                    else:
                        logger.warning(f"No handler for message type: {message.message_type}")
                        
                except Exception as e:
                    logger.error(f"Error processing message {message.id}: {e}")
            
            if messages:
                logger.debug(f"Processed {len(messages)} network messages")
                
        except Exception as e:
            logger.error(f"Error processing network messages: {e}")
        
        return state

    async def _handle_direct_message(self, state: NetworkDatageniusState, message: NetworkMessage) -> NetworkDatageniusState:
        """Handle direct message between agents."""
        # Add message to communication history
        state["communication_history"].append({
            "message_id": message.id,
            "sender": message.sender_agent,
            "recipient": message.recipient_agent,
            "type": message.message_type.value,
            "content": message.content,
            "timestamp": message.created_at.isoformat()
        })
        
        # Update agent connections
        sender_connections = state["agent_connections"].get(message.sender_agent, set())
        sender_connections.add(message.recipient_agent)
        state["agent_connections"][message.sender_agent] = sender_connections
        
        return state

    async def _handle_consultation_request(self, state: NetworkDatageniusState, message: NetworkMessage) -> NetworkDatageniusState:
        """Handle consultation request between agents."""
        consultation_data = message.content
        
        # Add to consultation requests if not already present
        existing_request = None
        for req in state.get("consultation_requests", []):
            if req.get("id") == consultation_data.get("consultation_id"):
                existing_request = req
                break
        
        if not existing_request:
            consultation_request = {
                "id": consultation_data.get("consultation_id"),
                "requesting_agent": message.sender_agent,
                "specialist_agent": message.recipient_agent,
                "topic": consultation_data.get("topic"),
                "context": consultation_data.get("context", {}),
                "urgency": consultation_data.get("urgency", "normal"),
                "status": "pending",
                "created_at": message.created_at.isoformat(),
                "message_id": message.id
            }
            
            if "consultation_requests" not in state:
                state["consultation_requests"] = []
            state["consultation_requests"].append(consultation_request)
        
        return state

    async def _handle_consultation_response(self, state: NetworkDatageniusState, message: NetworkMessage) -> NetworkDatageniusState:
        """Handle consultation response."""
        response_data = message.content
        consultation_id = response_data.get("consultation_id")
        
        # Update consultation request with response
        for req in state.get("consultation_requests", []):
            if req.get("id") == consultation_id:
                req["status"] = "completed"
                req["response"] = response_data.get("response")
                req["completed_at"] = message.created_at.isoformat()
                break
        
        return state

    async def _handle_delegation(self, state: NetworkDatageniusState, message: NetworkMessage) -> NetworkDatageniusState:
        """Handle task delegation."""
        delegation_data = message.content
        delegation_id = delegation_data.get("delegation_id")
        
        # Add to delegation tasks
        if "delegation_tasks" not in state:
            state["delegation_tasks"] = {}
        
        state["delegation_tasks"][delegation_id] = {
            "task_name": delegation_data.get("task_name"),
            "task_description": delegation_data.get("task_description"),
            "delegating_agent": message.sender_agent,
            "assigned_agent": message.recipient_agent,
            "status": "assigned",
            "priority": delegation_data.get("priority", "normal"),
            "deadline": delegation_data.get("deadline"),
            "created_at": message.created_at.isoformat(),
            "message_id": message.id
        }
        
        return state

    async def _handle_collaboration_invite(self, state: NetworkDatageniusState, message: NetworkMessage) -> NetworkDatageniusState:
        """Handle collaboration invite."""
        invite_data = message.content
        team_id = invite_data.get("team_id")
        
        # Update team information
        if "active_teams" not in state:
            state["active_teams"] = {}
        
        if team_id not in state["active_teams"]:
            state["active_teams"][team_id] = {
                "team_id": team_id,
                "task_description": invite_data.get("task_description"),
                "coordinator": invite_data.get("coordinator"),
                "members": invite_data.get("team_members", []),
                "status": "forming",
                "created_at": message.created_at.isoformat()
            }
        
        return state

    async def _handle_handoff(self, state: NetworkDatageniusState, message: NetworkMessage) -> NetworkDatageniusState:
        """Handle agent handoff."""
        handoff_data = message.content
        
        # Update current agent
        state["current_agent"] = message.recipient_agent
        
        # Add to handoff history
        if "handoff_history" not in state:
            state["handoff_history"] = []
        
        state["handoff_history"].append({
            "from_agent": message.sender_agent,
            "to_agent": message.recipient_agent,
            "reason": handoff_data.get("task_description"),
            "timestamp": message.created_at.isoformat(),
            "message_id": message.id
        })
        
        return state

    async def _handle_consensus_vote(self, state: NetworkDatageniusState, message: NetworkMessage) -> NetworkDatageniusState:
        """Handle consensus voting."""
        vote_data = message.content
        vote_id = vote_data.get("vote_id")
        
        # Update voting information
        if "active_votes" not in state:
            state["active_votes"] = {}
        
        if vote_id not in state["active_votes"]:
            state["active_votes"][vote_id] = {
                "vote_id": vote_id,
                "topic": vote_data.get("topic"),
                "options": vote_data.get("options", []),
                "votes": {},
                "status": "active",
                "created_at": message.created_at.isoformat()
            }
        
        # Record vote
        if "vote" in vote_data:
            state["active_votes"][vote_id]["votes"][message.sender_agent] = vote_data["vote"]
        
        return state

    async def _handle_insight_share(self, state: NetworkDatageniusState, message: NetworkMessage) -> NetworkDatageniusState:
        """Handle insight sharing."""
        insight_data = message.content
        
        # Add to network insights
        insight = {
            "id": message.id,
            "sharing_agent": message.sender_agent,
            "insight_type": insight_data.get("insight_type"),
            "content": insight_data.get("content"),
            "relevance_score": insight_data.get("relevance_score", 0.5),
            "timestamp": message.created_at.isoformat()
        }
        
        state["network_insights"].append(insight)
        
        return state

    async def _handle_status_update(self, state: NetworkDatageniusState, message: NetworkMessage) -> NetworkDatageniusState:
        """Handle agent status updates."""
        status_data = message.content
        agent_id = message.sender_agent
        
        # Update agent availability
        state["agent_availability"][agent_id] = status_data.get("available", True)
        
        # Update performance metrics if provided
        if "performance_metrics" in status_data:
            if "network_performance_metrics" not in state:
                state["network_performance_metrics"] = {}
            state["network_performance_metrics"][agent_id] = status_data["performance_metrics"]
        
        return state

    async def _handle_discovery_requests(self, state: NetworkDatageniusState) -> NetworkDatageniusState:
        """Handle agent discovery requests."""
        try:
            capability_requests = state.get("capability_requests", [])
            
            for request in capability_requests:
                if request.get("status") == "pending":
                    # Process discovery request
                    discovery_request = DiscoveryRequest(
                        requesting_agent=request.get("requesting_agent"),
                        required_capabilities=request.get("required_capabilities", []),
                        preferred_capabilities=request.get("preferred_capabilities"),
                        max_results=request.get("max_results", 5),
                        strategy=DiscoveryStrategy(request.get("strategy", "hybrid"))
                    )
                    
                    results = await discovery_service.discover_agents(discovery_request)
                    
                    # Update request with results
                    request["status"] = "completed"
                    request["results"] = [
                        {
                            "agent_id": result.agent.agent_id,
                            "agent_name": result.agent.name,
                            "match_score": result.match_score,
                            "recommendation_reason": result.recommendation_reason
                        }
                        for result in results
                    ]
                    request["completed_at"] = datetime.now().isoformat()
            
        except Exception as e:
            logger.error(f"Error handling discovery requests: {e}")
        
        return state

    async def _coordinate_workflows(self, state: NetworkDatageniusState) -> NetworkDatageniusState:
        """Coordinate active multi-agent workflows."""
        try:
            active_workflows = state.get("active_workflows", {})
            
            for workflow_id, workflow_data in active_workflows.items():
                if workflow_data.get("status") == "active":
                    # Check workflow progress
                    participants = workflow_data.get("participants", [])
                    completed_tasks = workflow_data.get("completed_tasks", 0)
                    total_tasks = workflow_data.get("total_tasks", 1)
                    
                    # Update workflow status based on progress
                    if completed_tasks >= total_tasks:
                        workflow_data["status"] = "completed"
                        workflow_data["completed_at"] = datetime.now().isoformat()
                    
                    # Update coordination state
                    state["coordination_state"]["active_workflows"] = len([
                        w for w in active_workflows.values() 
                        if w.get("status") == "active"
                    ])
            
        except Exception as e:
            logger.error(f"Error coordinating workflows: {e}")
        
        return state

    async def _update_network_health(self, state: NetworkDatageniusState) -> NetworkDatageniusState:
        """Update network health status."""
        try:
            # Calculate network health metrics
            total_agents = len(state.get("network_agents", {}))
            available_agents = sum(1 for available in state.get("agent_availability", {}).values() if available)
            
            health_score = available_agents / total_agents if total_agents > 0 else 0
            
            # Determine health status
            if health_score >= 0.8:
                health_status = "healthy"
            elif health_score >= 0.6:
                health_status = "degraded"
            else:
                health_status = "critical"
            
            state["network_health_status"] = {
                "status": health_status,
                "health_score": health_score,
                "total_agents": total_agents,
                "available_agents": available_agents,
                "last_check": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error updating network health: {e}")
        
        return state

    async def _determine_next_action(self, state: NetworkDatageniusState) -> Optional[str]:
        """Determine the next action based on network state."""
        try:
            # Check for pending handoffs
            handoff_history = state.get("handoff_history", [])
            if handoff_history:
                latest_handoff = handoff_history[-1]
                target_agent = latest_handoff.get("to_agent")
                if target_agent:
                    return f"route_to_{target_agent}"
            
            # Check for active consultations
            consultation_requests = state.get("consultation_requests", [])
            pending_consultations = [req for req in consultation_requests if req.get("status") == "pending"]
            if pending_consultations:
                return "process_consultation"
            
            # Check for team formation
            active_teams = state.get("active_teams", {})
            forming_teams = [team for team in active_teams.values() if team.get("status") == "forming"]
            if forming_teams:
                return "coordinate_team_formation"
            
            # Default to continuing with current agent
            current_agent = state.get("current_agent")
            if current_agent:
                return f"continue_with_{current_agent}"
            
            return None
            
        except Exception as e:
            logger.error(f"Error determining next action: {e}")
            return None


# Create router node instance
network_router_node = NetworkRouterNode()
