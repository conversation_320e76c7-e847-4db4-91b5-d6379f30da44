"""
Network Agent Registry for LangGraph-based Datagenius System.

This module provides a dynamic agent registry that enables network-style
communication where every agent can discover and communicate with every
other agent in the system.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Set, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import uuid
from collections import defaultdict

logger = logging.getLogger(__name__)


class AgentStatus(str, Enum):
    """Agent status in the network."""
    ACTIVE = "active"
    BUSY = "busy"
    IDLE = "idle"
    OFFLINE = "offline"
    ERROR = "error"


class CommunicationCapability(str, Enum):
    """Types of communication capabilities."""
    DIRECT_MESSAGE = "direct_message"
    CONSULTATION = "consultation"
    DELEGATION = "delegation"
    COLLABORATION = "collaboration"
    CONSENSUS_VOTING = "consensus_voting"
    INSIGHT_SHARING = "insight_sharing"


@dataclass
class AgentCapability:
    """Represents a specific capability of an agent."""
    name: str
    description: str
    confidence_level: float  # 0.0 to 1.0
    keywords: List[str] = field(default_factory=list)
    prerequisites: List[str] = field(default_factory=list)
    output_types: List[str] = field(default_factory=list)


@dataclass
class NetworkAgent:
    """Represents an agent in the network registry."""
    agent_id: str
    agent_type: str
    name: str
    description: str
    capabilities: List[AgentCapability] = field(default_factory=list)
    communication_capabilities: List[CommunicationCapability] = field(default_factory=list)
    status: AgentStatus = AgentStatus.IDLE
    current_load: int = 0
    max_concurrent_tasks: int = 5
    priority_level: int = 1  # 1=highest, 5=lowest
    specializations: List[str] = field(default_factory=list)
    collaboration_preferences: Dict[str, float] = field(default_factory=dict)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    last_active: datetime = field(default_factory=datetime.now)
    registration_time: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def can_handle_capability(self, capability_name: str, min_confidence: float = 0.7) -> bool:
        """Check if agent can handle a specific capability."""
        for cap in self.capabilities:
            if cap.name == capability_name and cap.confidence_level >= min_confidence:
                return True
        return False

    def get_capability_confidence(self, capability_name: str) -> float:
        """Get confidence level for a specific capability."""
        for cap in self.capabilities:
            if cap.name == capability_name:
                return cap.confidence_level
        return 0.0

    def is_available(self) -> bool:
        """Check if agent is available for new tasks."""
        return (
            self.status in [AgentStatus.ACTIVE, AgentStatus.IDLE] and
            self.current_load < self.max_concurrent_tasks
        )

    def update_performance_metric(self, metric_name: str, value: float):
        """Update a performance metric."""
        self.performance_metrics[metric_name] = value
        self.last_active = datetime.now()


class NetworkAgentRegistry:
    """
    Dynamic agent registry for network-style multi-agent communication.
    
    This registry enables:
    - Dynamic agent discovery and registration
    - Capability-based agent matching
    - Load balancing and availability tracking
    - Network topology management
    - Performance monitoring and optimization
    """

    def __init__(self):
        """Initialize the network agent registry."""
        self.agents: Dict[str, NetworkAgent] = {}
        self.capability_index: Dict[str, Set[str]] = defaultdict(set)
        self.communication_graph: Dict[str, Set[str]] = defaultdict(set)
        self.active_conversations: Dict[str, Dict[str, Any]] = {}
        self.performance_history: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        self.collaboration_patterns: Dict[str, Dict[str, float]] = defaultdict(dict)
        
        # Registry configuration
        self.max_inactive_time = timedelta(minutes=30)
        self.performance_history_limit = 100
        self.auto_cleanup_enabled = True
        
        # Event callbacks
        self.event_callbacks: Dict[str, List[Callable]] = defaultdict(list)
        
        # Start background tasks
        self._cleanup_task = None
        self._start_background_tasks()
        
        logger.info("NetworkAgentRegistry initialized")

    def _start_background_tasks(self):
        """Start background maintenance tasks."""
        if self.auto_cleanup_enabled:
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())

    async def _periodic_cleanup(self):
        """Periodic cleanup of inactive agents and old data."""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                await self._cleanup_inactive_agents()
                await self._cleanup_old_performance_data()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in periodic cleanup: {e}")

    async def register_agent(
        self,
        agent_id: str,
        agent_type: str,
        name: str,
        description: str,
        capabilities: List[Dict[str, Any]],
        communication_capabilities: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Register an agent in the network.
        
        Args:
            agent_id: Unique identifier for the agent
            agent_type: Type/category of the agent
            name: Human-readable name
            description: Description of the agent's purpose
            capabilities: List of capability dictionaries
            communication_capabilities: List of communication capability names
            metadata: Additional metadata
            
        Returns:
            True if registration successful, False otherwise
        """
        try:
            # Convert capability dictionaries to AgentCapability objects
            agent_capabilities = []
            for cap_dict in capabilities:
                capability = AgentCapability(
                    name=cap_dict.get("name", ""),
                    description=cap_dict.get("description", ""),
                    confidence_level=cap_dict.get("confidence_level", 0.8),
                    keywords=cap_dict.get("keywords", []),
                    prerequisites=cap_dict.get("prerequisites", []),
                    output_types=cap_dict.get("output_types", [])
                )
                agent_capabilities.append(capability)

            # Convert communication capabilities
            comm_caps = []
            if communication_capabilities:
                for cap_name in communication_capabilities:
                    try:
                        comm_caps.append(CommunicationCapability(cap_name))
                    except ValueError:
                        logger.warning(f"Unknown communication capability: {cap_name}")

            # Create network agent
            network_agent = NetworkAgent(
                agent_id=agent_id,
                agent_type=agent_type,
                name=name,
                description=description,
                capabilities=agent_capabilities,
                communication_capabilities=comm_caps,
                metadata=metadata or {}
            )

            # Register the agent
            self.agents[agent_id] = network_agent
            
            # Update capability index
            for capability in agent_capabilities:
                self.capability_index[capability.name].add(agent_id)
            
            # Initialize communication graph entry
            self.communication_graph[agent_id] = set()
            
            # Trigger registration event
            await self._trigger_event("agent_registered", {
                "agent_id": agent_id,
                "agent_type": agent_type,
                "capabilities": [cap.name for cap in agent_capabilities]
            })
            
            logger.info(f"Registered agent {agent_id} ({agent_type}) with {len(agent_capabilities)} capabilities")
            return True
            
        except Exception as e:
            logger.error(f"Error registering agent {agent_id}: {e}")
            return False

    async def _trigger_event(self, event_type: str, data: Dict[str, Any]):
        """Trigger event callbacks."""
        for callback in self.event_callbacks.get(event_type, []):
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(data)
                else:
                    callback(data)
            except Exception as e:
                logger.error(f"Error in event callback for {event_type}: {e}")

    async def _cleanup_inactive_agents(self):
        """Remove inactive agents from the registry."""
        current_time = datetime.now()
        inactive_agents = []
        
        for agent_id, agent in self.agents.items():
            if current_time - agent.last_active > self.max_inactive_time:
                inactive_agents.append(agent_id)
        
        for agent_id in inactive_agents:
            await self.unregister_agent(agent_id)
            logger.info(f"Removed inactive agent: {agent_id}")

    async def _cleanup_old_performance_data(self):
        """Clean up old performance data."""
        for agent_id in list(self.performance_history.keys()):
            history = self.performance_history[agent_id]
            if len(history) > self.performance_history_limit:
                self.performance_history[agent_id] = history[-self.performance_history_limit:]


    async def unregister_agent(self, agent_id: str) -> bool:
        """
        Unregister an agent from the network.

        Args:
            agent_id: ID of the agent to unregister

        Returns:
            True if unregistration successful, False otherwise
        """
        try:
            if agent_id not in self.agents:
                logger.warning(f"Agent {agent_id} not found in registry")
                return False

            agent = self.agents[agent_id]

            # Remove from capability index
            for capability in agent.capabilities:
                self.capability_index[capability.name].discard(agent_id)
                if not self.capability_index[capability.name]:
                    del self.capability_index[capability.name]

            # Remove from communication graph
            del self.communication_graph[agent_id]
            for other_agent_connections in self.communication_graph.values():
                other_agent_connections.discard(agent_id)

            # Clean up active conversations
            conversations_to_remove = []
            for conv_id, conv_data in self.active_conversations.items():
                if agent_id in conv_data.get("participants", []):
                    conversations_to_remove.append(conv_id)

            for conv_id in conversations_to_remove:
                del self.active_conversations[conv_id]

            # Remove agent
            del self.agents[agent_id]

            # Trigger unregistration event
            await self._trigger_event("agent_unregistered", {"agent_id": agent_id})

            logger.info(f"Unregistered agent {agent_id}")
            return True

        except Exception as e:
            logger.error(f"Error unregistering agent {agent_id}: {e}")
            return False

    def get_agent(self, agent_id: str) -> Optional[NetworkAgent]:
        """Get agent by ID."""
        return self.agents.get(agent_id)

    def get_all_agents(self) -> List[NetworkAgent]:
        """Get all registered agents."""
        return list(self.agents.values())

    def get_agents_by_type(self, agent_type: str) -> List[NetworkAgent]:
        """Get all agents of a specific type."""
        return [agent for agent in self.agents.values() if agent.agent_type == agent_type]

    def find_agents_by_capability(
        self,
        capability_name: str,
        min_confidence: float = 0.7,
        only_available: bool = True
    ) -> List[NetworkAgent]:
        """
        Find agents that have a specific capability.

        Args:
            capability_name: Name of the capability to search for
            min_confidence: Minimum confidence level required
            only_available: Whether to only return available agents

        Returns:
            List of matching agents sorted by confidence level
        """
        matching_agents = []

        for agent_id in self.capability_index.get(capability_name, set()):
            agent = self.agents.get(agent_id)
            if not agent:
                continue

            if only_available and not agent.is_available():
                continue

            confidence = agent.get_capability_confidence(capability_name)
            if confidence >= min_confidence:
                matching_agents.append(agent)

        # Sort by confidence level (descending)
        matching_agents.sort(key=lambda a: a.get_capability_confidence(capability_name), reverse=True)
        return matching_agents

    def find_best_agent_for_capabilities(
        self,
        required_capabilities: List[str],
        preferred_capabilities: Optional[List[str]] = None,
        exclude_agents: Optional[List[str]] = None
    ) -> Optional[NetworkAgent]:
        """
        Find the best agent for a set of capabilities.

        Args:
            required_capabilities: List of required capabilities
            preferred_capabilities: List of preferred capabilities
            exclude_agents: List of agent IDs to exclude

        Returns:
            Best matching agent or None if no suitable agent found
        """
        exclude_agents = exclude_agents or []
        preferred_capabilities = preferred_capabilities or []

        candidate_agents = []

        for agent in self.agents.values():
            if agent.agent_id in exclude_agents or not agent.is_available():
                continue

            # Check required capabilities
            required_score = 0
            for cap_name in required_capabilities:
                confidence = agent.get_capability_confidence(cap_name)
                if confidence < 0.7:  # Minimum threshold for required capabilities
                    required_score = -1
                    break
                required_score += confidence

            if required_score < 0:
                continue

            # Check preferred capabilities
            preferred_score = 0
            for cap_name in preferred_capabilities:
                preferred_score += agent.get_capability_confidence(cap_name)

            # Calculate total score
            total_score = required_score * 2 + preferred_score  # Weight required capabilities more

            # Add performance bonus
            avg_performance = sum(agent.performance_metrics.values()) / len(agent.performance_metrics) if agent.performance_metrics else 0.5
            total_score += avg_performance

            candidate_agents.append((agent, total_score))

        if not candidate_agents:
            return None

        # Return agent with highest score
        candidate_agents.sort(key=lambda x: x[1], reverse=True)
        return candidate_agents[0][0]

    async def update_agent_status(self, agent_id: str, status: AgentStatus) -> bool:
        """Update agent status."""
        if agent_id not in self.agents:
            return False

        old_status = self.agents[agent_id].status
        self.agents[agent_id].status = status
        self.agents[agent_id].last_active = datetime.now()

        await self._trigger_event("agent_status_changed", {
            "agent_id": agent_id,
            "old_status": old_status.value,
            "new_status": status.value
        })

        return True

    async def update_agent_load(self, agent_id: str, load_change: int) -> bool:
        """Update agent's current load."""
        if agent_id not in self.agents:
            return False

        agent = self.agents[agent_id]
        agent.current_load = max(0, agent.current_load + load_change)
        agent.last_active = datetime.now()

        # Auto-update status based on load
        if agent.current_load >= agent.max_concurrent_tasks:
            await self.update_agent_status(agent_id, AgentStatus.BUSY)
        elif agent.current_load == 0:
            await self.update_agent_status(agent_id, AgentStatus.IDLE)
        else:
            await self.update_agent_status(agent_id, AgentStatus.ACTIVE)

        return True

    def establish_communication_link(self, agent1_id: str, agent2_id: str) -> bool:
        """Establish bidirectional communication link between two agents."""
        if agent1_id not in self.agents or agent2_id not in self.agents:
            return False

        self.communication_graph[agent1_id].add(agent2_id)
        self.communication_graph[agent2_id].add(agent1_id)

        # Update collaboration patterns
        self.collaboration_patterns[agent1_id][agent2_id] = self.collaboration_patterns[agent1_id].get(agent2_id, 0) + 1
        self.collaboration_patterns[agent2_id][agent1_id] = self.collaboration_patterns[agent2_id].get(agent1_id, 0) + 1

        return True

    def get_connected_agents(self, agent_id: str) -> List[str]:
        """Get list of agents connected to the specified agent."""
        return list(self.communication_graph.get(agent_id, set()))

    def get_network_topology(self) -> Dict[str, List[str]]:
        """Get the current network topology."""
        return {agent_id: list(connections) for agent_id, connections in self.communication_graph.items()}

    def get_registry_stats(self) -> Dict[str, Any]:
        """Get registry statistics."""
        total_agents = len(self.agents)
        active_agents = len([a for a in self.agents.values() if a.status == AgentStatus.ACTIVE])
        busy_agents = len([a for a in self.agents.values() if a.status == AgentStatus.BUSY])
        idle_agents = len([a for a in self.agents.values() if a.status == AgentStatus.IDLE])

        agent_types = {}
        for agent in self.agents.values():
            agent_types[agent.agent_type] = agent_types.get(agent.agent_type, 0) + 1

        total_capabilities = len(self.capability_index)
        total_connections = sum(len(connections) for connections in self.communication_graph.values()) // 2

        return {
            "total_agents": total_agents,
            "active_agents": active_agents,
            "busy_agents": busy_agents,
            "idle_agents": idle_agents,
            "agent_types": agent_types,
            "total_capabilities": total_capabilities,
            "total_connections": total_connections,
            "active_conversations": len(self.active_conversations)
        }

    def subscribe_to_events(self, event_type: str, callback: Callable):
        """Subscribe to registry events."""
        self.event_callbacks[event_type].append(callback)

    def unsubscribe_from_events(self, event_type: str, callback: Callable):
        """Unsubscribe from registry events."""
        if callback in self.event_callbacks[event_type]:
            self.event_callbacks[event_type].remove(callback)


# Global registry instance
network_registry = NetworkAgentRegistry()
